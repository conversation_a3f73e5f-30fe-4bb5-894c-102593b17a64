'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { initializeMixpanelTracking } from '@/common/utils/mixpanel/eventTriggers';
import { MixpanelIdentityManager } from '@/common/utils/mixpanel/identityManager';

/**
 * MixpanelInitializer Component
 * 
 * This component handles the initialization of Mixpanel tracking based on user authentication status.
 * It should be included in the root layout to ensure proper tracking initialization.
 * 
 * Behavior:
 * - For unauthenticated users: Initializes anonymous tracking
 * - For authenticated users: Ensures proper user identification
 * - Handles the transition between anonymous and authenticated states
 */
export const MixpanelInitializer = () => {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    if (status === 'loading') {
      // Still loading session, don't initialize yet
      return;
    }

    if (status === 'unauthenticated' || !session?.user) {
      // User is not authenticated, initialize anonymous tracking
      initializeMixpanelTracking();
    } else if (status === 'authenticated' && session.user.id) {
      // User is authenticated, ensure proper identification
      const userProps = {
        $email: session.user.email,
        $name: `${session.user.firstName || ''} ${session.user.lastName || ''}`.trim(),
        role: session.user.role,
        investorType: session.user.investorType,
        isOnboarded: session.user.isOnboarded,
      };

      // Handle user login (this will only identify if not already identified)
      MixpanelIdentityManager.handleUserLogin(session.user.id, userProps);
    }
  }, [session, status]);

  // This component doesn't render anything
  return null;
};

export default MixpanelInitializer;
