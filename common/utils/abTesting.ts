import {
  mixpanelCustomEvent,
  MixpanelEventName,
} from "@/common/utils/mixpanel/eventTriggers";
import { ButtonDistribution } from "@/common/hooks/useButtonDistribution";

export type ButtonType = "watchlist" | "rate_showcase";

export interface ABTestResult {
  buttonType: ButtonType;
  shouldShowRateButton: boolean;
}

export const determineButtonDisplay = (
  distribution: ButtonDistribution,
  slug: string,
  userId?: string,
): ABTestResult => {
  try {
    const storedDecision = getStoredButtonDecision(userId, slug);
    if (storedDecision) {
      return storedDecision;
    }

    let watchlistProbability = 0.5;

    if (distribution.total > 10) {
      const watchlistPercentage = distribution.watchlistPercentage;

      if (watchlistPercentage > 55) {
        watchlistProbability = 0.3;
      } else if (watchlistPercentage < 45) {
        watchlistProbability = 0.7;
      }
    }

    const seed = userId || getSessionSeed();
    const random = seededRandom(seed + slug);

    const showWatchlist = random < watchlistProbability;
    const buttonType: ButtonType = showWatchlist
      ? "watchlist"
      : "rate_showcase";

    const result: ABTestResult = {
      buttonType,
      shouldShowRateButton: !showWatchlist,
    };

    storeButtonDecision(userId, slug, result);
    trackButtonDisplay(buttonType, slug, distribution);

    return result;
  } catch (error) {
    console.error("Error in determineButtonDisplay:", error);

    const fallbackRandom = Math.random();
    const buttonType: ButtonType =
      fallbackRandom < 0.5 ? "watchlist" : "rate_showcase";

    const result: ABTestResult = {
      buttonType,
      shouldShowRateButton: buttonType === "rate_showcase",
    };

    storeButtonDecision(userId, slug, result);
    trackButtonDisplay(buttonType, slug, distribution);

    return result;
  }
};

const trackButtonDisplay = (
  buttonType: ButtonType,
  slug: string,
  distribution: ButtonDistribution,
) => {
  if (typeof window === "undefined") {
    return;
  }

  const trackingKey = `button-display-tracked-${slug}`;

  const alreadyTracked = localStorage.getItem(trackingKey);

  if (alreadyTracked) {
    return;
  }

  mixpanelCustomEvent({
    eventName: MixpanelEventName.buttonDisplayed,
    mixpanelProps: {
      page: "Showcase",
      slug: slug,
      buttonType: buttonType,
      currentWatchlistPercentage: distribution.watchlistPercentage,
      currentRateShowcasePercentage: distribution.rateShowcasePercentage,
      totalEvents: distribution.total,
    },
  });

  localStorage.setItem(trackingKey, "true");
};

const getSessionSeed = (): string => {
  if (typeof window === "undefined") {
    return "server-fallback";
  }

  let seed = localStorage.getItem("ab-test-seed");
  if (!seed) {
    seed = Math.random().toString(36).substring(2, 15);
    localStorage.setItem("ab-test-seed", seed);
  }
  return seed;
};

const seededRandom = (seed: string): number => {
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash;
  }

  const normalized = Math.abs(hash) / 2147483647;
  return normalized;
};

const getStoredButtonDecision = (
  userId?: string,
  slug?: string,
): ABTestResult | null => {
  if (typeof window === "undefined" || !userId || !slug) {
    return null;
  }

  try {
    const storageKey = `ab-test-button-${userId}-${slug}`;
    const stored = localStorage.getItem(storageKey);

    if (stored) {
      return JSON.parse(stored) as ABTestResult;
    }
  } catch (error) {
    console.error("Error retrieving stored button decision:", error);
  }

  return null;
};

const storeButtonDecision = (
  userId?: string,
  slug?: string,
  result?: ABTestResult,
): void => {
  if (typeof window === "undefined" || !userId || !slug || !result) {
    return;
  }

  try {
    const storageKey = `ab-test-button-${userId}-${slug}`;
    localStorage.setItem(storageKey, JSON.stringify(result));
  } catch (error) {
    console.error("Error storing button decision:", error);
  }
};

export const shouldShowBothButtons = (isAuthenticated: boolean): boolean => {
  return isAuthenticated;
};
