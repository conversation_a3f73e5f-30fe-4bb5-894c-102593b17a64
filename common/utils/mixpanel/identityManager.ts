import { Mixpanel } from "./index";
import { v4 as uuidv4 } from "uuid";

// Storage keys for identity management
const ANONYMOUS_USER_ID_KEY = "mixpanel_anonymous_id";
const LAST_IDENTIFIED_USER_KEY = "mixpanel_last_identified_user";
const ALIAS_CREATED_KEY = "mixpanel_alias_created";
const SESSION_RECORDING_KEY = "mixpanel_session_recording_started";

/**
 * Mixpanel Identity Manager
 * Handles the complete lifecycle of user identity in Mixpanel following best practices:
 * 1. Anonymous user tracking with persistent ID
 * 2. Proper aliasing when user signs up
 * 3. Identification when user logs in
 * 4. Avoiding duplicate aliases
 */
export class MixpanelIdentityManager {
  /**
   * Get or create anonymous user ID
   * This should be called when the app first loads for anonymous tracking
   */
  static getAnonymousId(): string {
    let anonymousId = localStorage.getItem(ANONYMOUS_USER_ID_KEY);
    
    if (!anonymousId) {
      anonymousId = uuidv4();
      localStorage.setItem(ANONYMOUS_USER_ID_KEY, anonymousId);
    }
    
    return anonymousId;
  }

  /**
   * Initialize anonymous tracking
   * Call this when the app loads and user is not authenticated
   */
  static initializeAnonymousTracking(): void {
    try {
      const anonymousId = this.getAnonymousId();
      
      // Only identify if we haven't identified this anonymous user yet
      const lastIdentifiedUser = localStorage.getItem(LAST_IDENTIFIED_USER_KEY);
      
      if (lastIdentifiedUser !== anonymousId) {
        Mixpanel.identify(anonymousId);
        localStorage.setItem(LAST_IDENTIFIED_USER_KEY, anonymousId);
      }
    } catch (error) {
      console.error("Error initializing anonymous tracking:", error);
    }
  }

  /**
   * Handle user signup - creates alias between anonymous and authenticated identity
   * Call this when a user successfully signs up
   */
  static handleUserSignup(userId: string, userProperties: Record<string, any> = {}): void {
    try {
      const anonymousId = this.getAnonymousId();
      const aliasKey = `${ALIAS_CREATED_KEY}_${userId}`;
      
      // Check if we've already created an alias for this user
      const aliasAlreadyCreated = localStorage.getItem(aliasKey) === "true";
      
      if (!aliasAlreadyCreated) {
        // Create alias to connect anonymous identity with authenticated identity
        Mixpanel.alias(userId);
        localStorage.setItem(aliasKey, "true");
        
        // Now identify with the real user ID
        Mixpanel.identify(userId);
        localStorage.setItem(LAST_IDENTIFIED_USER_KEY, userId);
        
        // Set user properties
        if (Object.keys(userProperties).length > 0) {
          Mixpanel.people.set(userProperties);
        }
        
        // Start session recording if not already started
        this.startSessionRecording();
        
        console.log(`Mixpanel: Created alias from ${anonymousId} to ${userId}`);
      } else {
        // If alias already exists, just identify
        this.handleUserLogin(userId, userProperties);
      }
    } catch (error) {
      console.error("Error handling user signup:", error);
    }
  }

  /**
   * Handle user login - identifies existing user
   * Call this when a user successfully logs in (not signup)
   */
  static handleUserLogin(userId: string, userProperties: Record<string, any> = {}): void {
    try {
      const lastIdentifiedUser = localStorage.getItem(LAST_IDENTIFIED_USER_KEY);
      
      // Only identify if this is a different user or first time
      if (lastIdentifiedUser !== userId) {
        // If switching users, reset first
        if (lastIdentifiedUser && lastIdentifiedUser !== this.getAnonymousId()) {
          Mixpanel.reset();
        }
        
        Mixpanel.identify(userId);
        localStorage.setItem(LAST_IDENTIFIED_USER_KEY, userId);
        
        // Update user properties
        if (Object.keys(userProperties).length > 0) {
          Mixpanel.people.set(userProperties);
        }
        
        // Start session recording if not already started
        this.startSessionRecording();
        
        console.log(`Mixpanel: Identified user ${userId}`);
      }
    } catch (error) {
      console.error("Error handling user login:", error);
    }
  }

  /**
   * Handle user logout - resets to anonymous tracking
   * Call this when a user logs out
   */
  static handleUserLogout(): void {
    try {
      // Reset Mixpanel state
      Mixpanel.reset();
      
      // Clear last identified user
      localStorage.removeItem(LAST_IDENTIFIED_USER_KEY);
      
      // Clear session recording flag
      localStorage.removeItem(SESSION_RECORDING_KEY);
      
      // Reinitialize anonymous tracking
      this.initializeAnonymousTracking();
      
      console.log("Mixpanel: User logged out, reset to anonymous tracking");
    } catch (error) {
      console.error("Error handling user logout:", error);
    }
  }

  /**
   * Get current user ID (anonymous or authenticated)
   */
  static getCurrentUserId(): string {
    const lastIdentifiedUser = localStorage.getItem(LAST_IDENTIFIED_USER_KEY);
    return lastIdentifiedUser || this.getAnonymousId();
  }

  /**
   * Check if current user is authenticated (not anonymous)
   */
  static isUserAuthenticated(): boolean {
    const lastIdentifiedUser = localStorage.getItem(LAST_IDENTIFIED_USER_KEY);
    const anonymousId = localStorage.getItem(ANONYMOUS_USER_ID_KEY);
    
    return lastIdentifiedUser !== null && lastIdentifiedUser !== anonymousId;
  }

  /**
   * Start session recording if not already started
   */
  private static startSessionRecording(): void {
    try {
      const sessionRecordingStarted = localStorage.getItem(SESSION_RECORDING_KEY) === "true";
      
      if (!sessionRecordingStarted) {
        Mixpanel.start_session_recording();
        localStorage.setItem(SESSION_RECORDING_KEY, "true");
      }
    } catch (error) {
      console.error("Error starting session recording:", error);
    }
  }

  /**
   * Clear all identity data (for testing or complete reset)
   */
  static clearAllIdentityData(): void {
    try {
      localStorage.removeItem(ANONYMOUS_USER_ID_KEY);
      localStorage.removeItem(LAST_IDENTIFIED_USER_KEY);
      localStorage.removeItem(SESSION_RECORDING_KEY);
      
      // Clear all alias flags
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(ALIAS_CREATED_KEY)) {
          localStorage.removeItem(key);
        }
      });
      
      Mixpanel.reset();
      
      console.log("Mixpanel: All identity data cleared");
    } catch (error) {
      console.error("Error clearing identity data:", error);
    }
  }
}
