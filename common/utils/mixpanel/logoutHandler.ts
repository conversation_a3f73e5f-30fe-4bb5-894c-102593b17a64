import { signOut } from 'next-auth/react';
import { mixpanelLogoutEvent } from './eventTriggers';
import { MixpanelEventName } from './eventTriggers';

/**
 * Handle user logout with proper Mixpanel tracking
 * This function should be called whenever a user logs out
 * 
 * @param redirectUrl - Optional URL to redirect to after logout
 */
export const handleUserLogout = async (redirectUrl?: string) => {
  try {
    // Track logout event
    mixpanelLogoutEvent(MixpanelEventName.logout);
    
    // Sign out using NextAuth
    await signOut({ 
      redirect: !!redirectUrl,
      callbackUrl: redirectUrl 
    });
  } catch (error) {
    console.error('Error during logout:', error);
    
    // Still attempt to sign out even if tracking fails
    await signOut({ 
      redirect: !!redirectUrl,
      callbackUrl: redirectUrl 
    });
  }
};

/**
 * Handle user logout without redirect
 * Useful for programmatic logouts where you want to handle navigation manually
 */
export const handleUserLogoutNoRedirect = async () => {
  try {
    // Track logout event
    mixpanelLogoutEvent(MixpanelEventName.logout);
    
    // Sign out without redirect
    await signOut({ redirect: false });
  } catch (error) {
    console.error('Error during logout:', error);
    
    // Still attempt to sign out even if tracking fails
    await signOut({ redirect: false });
  }
};
